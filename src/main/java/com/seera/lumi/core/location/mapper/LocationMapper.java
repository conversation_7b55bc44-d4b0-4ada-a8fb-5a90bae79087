package com.seera.lumi.core.location.mapper;

import com.seera.lumi.core.location.domain.Location;
import com.seera.lumi.core.location.dto.LocationDTO;
import com.seera.lumi.core.location.dto.MultilingualDTO;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.*;

import java.util.List;

@Mapper(
    componentModel = "spring",
    unmappedTargetPolicy = ReportingPolicy.IGNORE, uses = {CityMapper.class})
public interface LocationMapper {

  @Mapping(target = "name", source = "location", qualifiedByName = "toLocationName")
  LocationDTO toDto(Location location);

  List<LocationDTO> toDtos(List<Location> locations);

  @Named("toLocationName")
  default MultilingualDTO toLocationName(Location location) {
    if (location == null
            || (StringUtils.isEmpty(location.getNameEn()) && StringUtils.isEmpty(location.getNameAr()))) {
      return null;
    }
    return new MultilingualDTO(location.getNameEn(), location.getNameAr());
  }

  @Mapping(target = "nameEn", source = "name.en")
  @Mapping(target = "nameAr", source = "name.ar")
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "version", ignore = true)
  @Mapping(target = "createdOn", ignore = true)
  @Mapping(target = "updatedOn", ignore = true)
  Location toNewEntity(LocationDTO locationDTO);

  @Mapping(target = "nameEn", source = "name.en")
  @Mapping(target = "nameAr", source = "name.ar")
  @Mapping(target = "id", ignore = true)
  @Mapping(target = "version", ignore = true)
  @Mapping(target = "createdOn", ignore = true)
  @Mapping(target = "updatedOn", ignore = true)
  void updateEntity(@MappingTarget Location existingLocation, LocationDTO locationDTO);
}
