package com.seera.lumi.core.location.service.impl;

import com.seera.lumi.core.location.repository.LocationRepository;
import com.seera.lumi.core.location.domain.Location;
import com.seera.lumi.core.location.dto.LocationDTO;
import com.seera.lumi.core.location.mapper.LocationMapper;
import com.seera.lumi.core.location.service.errors.BaseError;
import com.seera.lumi.core.location.service.errors.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class LocationService {
  private final LocationRepository locationRepository;
  private final LocationMapper locationMapper;

  public List<LocationDTO> getAllLocations() {
    List<Location> locations = locationRepository.findAll();
    return locationMapper.toDtos(locations);
  }

  public LocationDTO findById(Integer id) {
    return locationRepository
        .findById(id)
        .map(locationMapper::toDto)
        .orElseThrow(
            () ->
                new BusinessException(
                    BaseError.RESOURCE_NOT_FOUND, Map.of("resource", "location")));
  }

  public LocationDTO createLocation(LocationDTO locationDTO) {
    Location location = locationMapper.toNewEntity(locationDTO);
    Location savedLocation = locationRepository.saveAndFlush(location);
    return locationMapper.toDto(savedLocation);
  }

  public LocationDTO updateLocation(Integer id, LocationDTO locationDTO) {
    Location existingLocation =
        locationRepository
            .findById(id)
            .orElseThrow(
                () ->
                    new BusinessException(
                        BaseError.RESOURCE_NOT_FOUND, Map.of("resource", "location")));

    locationMapper.updateEntity(existingLocation, locationDTO);
    Location updatedLocation = locationRepository.saveAndFlush(existingLocation);
    return locationMapper.toDto(updatedLocation);
  }

}
