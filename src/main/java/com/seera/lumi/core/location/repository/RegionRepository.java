package com.seera.lumi.core.location.repository;

import com.seera.lumi.core.location.domain.Region;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RegionRepository extends JpaRepository<Region, Integer> {
  @Query("SELECT r FROM Region r")
  List<Region> findAllRegions();
}
