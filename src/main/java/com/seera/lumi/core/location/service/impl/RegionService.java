package com.seera.lumi.core.location.service.impl;

import com.seera.lumi.core.location.repository.RegionRepository;
import com.seera.lumi.core.location.domain.Region;
import com.seera.lumi.core.location.dto.RegionDTO;
import com.seera.lumi.core.location.mapper.RegionMapper;
import com.seera.lumi.core.location.service.errors.BaseError;
import com.seera.lumi.core.location.service.errors.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class RegionService {
  private final RegionRepository regionRepository;
  private final RegionMapper regionMapper;

  public List<RegionDTO> getAllRegions() {
    List<Region> regionList = regionRepository.findAllRegions();
    return regionMapper.toDtos(regionList);
  }

  public RegionDTO findById(Integer id) {
    return regionRepository
        .findById(id)
        .map(regionMapper::toDto)
        .orElseThrow(
            () ->
                new BusinessException(BaseError.RESOURCE_NOT_FOUND, Map.of("resource", "region")));
  }
}
