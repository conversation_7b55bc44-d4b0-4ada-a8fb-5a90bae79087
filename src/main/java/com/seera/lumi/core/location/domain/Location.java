package com.seera.lumi.core.location.domain;

import com.seera.lumi.core.location.enums.LocationType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "location")
public class Location extends BaseEntity {

  @Column(name = "name_en", nullable = false)
  private String nameEn;

  @Column(name = "name_ar")
  private String nameAr;

  @Column(name = "location_type", length = 32)
  @Enumerated(EnumType.STRING)
  private LocationType locationType;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "city_id")
  private City city;

  @Column(name = "latitude")
  private Double latitude;

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "carpro_id", unique = true)
  private Integer carproId;

  @Column(name = "facility_id", length = 8, nullable = false, unique = true)
  private String facilityId;

  @Column(name = "sap_id", length = 8)
  private String sapId;

  @Column(name = "sap_account_no", length = 16)
  private String sapAccountNo;

  @Column(name = "enabled", nullable = false)
  private boolean enabled = false;
}
