package com.seera.lumi.core.location.service.impl;

import com.seera.lumi.core.location.repository.CityRepository;
import com.seera.lumi.core.location.domain.City;
import com.seera.lumi.core.location.dto.CityDTO;
import com.seera.lumi.core.location.mapper.CityMapper;
import com.seera.lumi.core.location.service.errors.BaseError;
import com.seera.lumi.core.location.service.errors.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class CityService {
  private final CityRepository cityRepository;
  private final CityMapper cityMapper;
  private final RegionService regionService;

  public List<CityDTO> getAllCities() {
    List<City> cityList = cityRepository.findAll();
    return cityMapper.toDtos(cityList);
  }

  public CityDTO findById(Integer id) {
    return cityRepository
            .findById(id)
            .map(cityMapper::toDto)
            .orElseThrow(
                    () ->
                            new BusinessException(BaseError.RESOURCE_NOT_FOUND, Map.of("resource", "city")));
  }
}
